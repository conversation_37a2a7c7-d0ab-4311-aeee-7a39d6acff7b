from rest_framework import routers
from django.urls import path
from api.crm.views import (
    template as template_views,
    event_reminder as event_reminder_views,
)
from api.crm.views import broadcast_message as broadcast_message_views
from api.crm.views import contact as contact_views
from api.crm.views import educational_institution as EducationalInstitutionViews
from api.crm.views import major as major_views
from api.crm.views import term as term_views
from api.crm.views import order as order_views
from api.crm.views import order_item as order_item_views
from api.crm.views import payment as payment_views
from api.crm.views import payment_method as payment_method_views
from api.crm.views import activity as activity_views
from api.crm.views import staff as staff_views
from api.crm.views import offering as offering_views
from api.crm.views import benefit as benefit_views
from api.crm.views import event as event_views
from api.crm.views import lead_source as lead_source_views
from api.crm.views import event_schedule as event_schedule_views
from api.crm.views.partnership import CrmPartnershipViewSet
from api.crm.views.events_alliances import (
    GetCurrentEventsAlliancesView,
    GetEventRegistrationsAlliancesView,
    GetEventsAlliancesView,
)
from api.crm.views.sales import (
    GetSoldSalesView,
    GetProspectSalesView,
    GetInterestedSalesView,
    GetPendingPaymentSalesView,
)
from api.crm.views.courses import GetCoursesView
from api.crm.views.dashboard.contact import CrmDashboardContactViewSet
from api.crm.views.dashboard.sales import CrmDashboardSalesViewSet

router = routers.DefaultRouter(trailing_slash=False)

router.register(
    r"templates",
    template_views.CrmTemplateViewSet,
    basename="templates",
)

router.register(
    r"contacts",
    contact_views.CrmContactViewSet,
    basename="crm-contacts",
)
router.register(
    r"educational-institutions",
    EducationalInstitutionViews.CrmEducationalInstitutionViewSet,
    basename="crm-educational-institutions",
)
router.register(
    r"partnerships",
    CrmPartnershipViewSet,
    basename="crm-partnerships",
)
router.register(
    r"majors",
    major_views.CrmMajorViewSet,
    basename="crm-majors",
)
router.register(
    r"payment-methods",
    payment_method_views.CrmPaymentMethodViewSet,
    basename="crm-payment-methods",
)
router.register(
    r"terms",
    term_views.CrmTermViewSet,
    basename="crm-terms",
)

router.register(
    r"orders",
    order_views.CrmOrderViewSet,
    basename="crm-orders",
)

router.register(
    r"order-items",
    order_item_views.CrmOrderItemViewSet,
    basename="crm-order-items",
)

router.register(
    r"lead-sources",
    lead_source_views.CrmLeadSourceViewSet,
    basename="crm-lead-sources",
)

router.register(
    r"staff",
    staff_views.CrmStaffViewSet,
    basename="crm-staff",
)

router.register(
    r"payments",
    payment_views.CrmPaymentViewSet,
    basename="crm-payments",
)

router.register(
    r"activities",
    activity_views.CrmActivityViewSet,
    basename="crm-activities",
)

router.register(
    r"event-reminders",
    event_reminder_views.CrmEventReminderViewSet,
    basename="event-reminders",
)

router.register(
    r"broadcast-messages",
    broadcast_message_views.CrmBroadcastMessageViewSet,
    basename="broadcast-messages",
)

router.register(
    r"offerings",
    offering_views.CrmOfferingViewSet,
    basename="crm-offerings",
)

router.register(
    r"events",
    event_views.CrmEventViewSet,
    basename="crm-events",
)

router.register(
    r"event-schedules",
    event_schedule_views.CrmEventScheduleViewSet,
    basename="crm-event-schedules",
)

router.register(
    r"benefits",
    benefit_views.CrmBenefitViewSet,
    basename="crm-benefits",
)

router.register(
    r"dashboard/contacts",
    CrmDashboardContactViewSet,
    basename="crm-dashboard-contacts",
)

router.register(
    r"dashboard/sales",
    CrmDashboardSalesViewSet,
    basename="crm-dashboard-sales",
)

urlpatterns = [
    *router.urls,
    path(
        "events-alliances/", GetEventsAlliancesView.as_view(), name="events-alliances"
    ),
    path(
        "events-alliances/current/",
        GetCurrentEventsAlliancesView.as_view(),
        name="current-events-alliances",
    ),
    path(
        "events-alliances/<int:event_id>/registrations/",
        GetEventRegistrationsAlliancesView.as_view(),
        name="event-registrations-alliances",
    ),
    path("sales-pipeline/sold/", GetSoldSalesView.as_view(), name="sold-sales"),
    path(
        "sales-pipeline/prospect/",
        GetProspectSalesView.as_view(),
        name="prospect-sales",
    ),
    path(
        "sales-pipeline/interested/",
        GetInterestedSalesView.as_view(),
        name="interested-sales",
    ),
    path(
        "sales-pipeline/pending-payment/",
        GetPendingPaymentSalesView.as_view(),
        name="pending-payment-sales",
    ),
    path("courses/", GetCoursesView.as_view(), name="courses"),
]
