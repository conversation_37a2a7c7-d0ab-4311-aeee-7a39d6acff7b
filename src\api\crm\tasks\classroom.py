from celery import shared_task
from django.core.mail import EmailMessage
from django.conf import settings
from django.template.loader import render_to_string
from core.models import Order
from services.google.classroom import GoogleClassroomManager


@shared_task
def send_classroom_invitation(email: str, class_code: str):
    """
    Tarea para enviar invitación a Google Classroom.

    Args:
        order_id (str): ID de la orden
        course_id (str): ID del curso de Google Classroom
    """
    try:
        classroom_manager = GoogleClassroomManager()

        course_id = classroom_manager.get_course_by_enrollment_code(class_code).get(
            "id"
        )

        if not course_id:
            raise Exception(f"No se encontró curso con código: {class_code}")

        # Enviar la invitación a Google Classroom
        invitation_result = classroom_manager.invite_student_to_course(course_id, email)

        if not invitation_result:
            raise Exception(
                f"No se pudo enviar la invitación de Google Classroom a {email}"
            )

        # Si la invitación fue exitosa o el usuario ya está inscrito, guardar en la base de datos

        return {
            "success": True,
            "message": f"Invitación enviada exitosamente a {email}",
            "invitation_status": invitation_result.get("status", "sent"),
        }

    except Exception as e:
        error_msg = f"Error al enviar invitación de Google Classroom: {str(e)}"
        print(error_msg)
        raise Exception(error_msg)


@shared_task
def process_classroom_invitation_for_order(order_id):
    """
    Procesa la invitación de Google Classroom para una orden específica.
    Verifica que la orden tenga productos con referencia externa (ext_reference)
    y envía las invitaciones correspondientes.

    Args:
        order_id (str): ID de la orden
    """
    try:
        order = Order.objects.get(oid=order_id)
        user = order.owner

        # Validar que el usuario tenga datos completos
        if not user.first_name or not user.last_name:
            raise Exception(f"El usuario debe tener nombre y apellido completos")

        if not user.email or not user.email.lower().endswith("@gmail.com"):
            raise Exception(f"El usuario debe tener un correo de Gmail válido")

        # Obtener todos los items de la orden
        order_items = order.items.filter(deleted=False)

        invitations_sent = []

        for item in order_items:
            offering = item.offering

            # Verificar si el offering tiene referencia externa (course_id)
            if offering.ext_reference:
                try:
                    # Enviar invitación para este curso
                    result = send_classroom_invitation.delay(
                        email=user.email,
                        class_code=offering.ext_reference,
                    )

                    invitations_sent.append(
                        {
                            "offering_name": offering.name,
                            "class_code": offering.ext_reference,
                            "task_id": result.id,
                        }
                    )

                except Exception as e:
                    print(
                        f"Error al procesar invitación para {offering.name}: {str(e)}"
                    )
                    continue

        if not invitations_sent:
            print(
                f"No se encontraron cursos con referencia externa para la orden {order_id}"
            )
            return {
                "success": False,
                "message": "No se encontraron cursos con referencia externa",
                "invitations_sent": [],
            }

        return {
            "success": True,
            "message": f"Se procesaron {len(invitations_sent)} invitaciones",
            "invitations_sent": invitations_sent,
        }

    except Order.DoesNotExist:
        error_msg = f"Orden con ID {order_id} no encontrada"
        print(error_msg)
        raise Exception(error_msg)
    except Exception as e:
        error_msg = f"Error al procesar invitaciones de Google Classroom: {str(e)}"
        print(error_msg)
        raise Exception(error_msg)
