from celery import shared_task
from django.core.mail import EmailMessage
from django.conf import settings
from django.template.loader import render_to_string
from core.models import Order
from services.google.classroom import GoogleClassroomManager


@shared_task
def send_classroom_invitation_email(order_id, course_id, course_name=None):
    """
    Tarea para enviar correo de invitación a Google Classroom después de que una orden sea marcada como vendida.
    
    Args:
        order_id (str): ID de la orden
        course_id (str): ID del curso de Google Classroom
        course_name (str, optional): Nombre del curso
    """
    try:
        # Obtener la orden
        order = Order.objects.get(oid=order_id)
        user = order.owner
        
        # Validar que el usuario tenga email de Gmail
        if not user.email or not user.email.lower().endswith('@gmail.com'):
            raise Exception(f"El usuario {user.get_full_name()} no tiene un correo de Gmail válido")
        
        # Inicializar el manager de Google Classroom
        classroom_manager = GoogleClassroomManager()
        
        # Obtener información del curso si no se proporciona el nombre
        if not course_name:
            course_info = classroom_manager.get_course_info(course_id)
            course_name = course_info.get('name', 'Curso') if course_info else 'Curso'
        
        # Enviar la invitación a Google Classroom
        invitation_result = classroom_manager.invite_student_to_course(course_id, user.email)
        
        if not invitation_result:
            raise Exception(f"No se pudo enviar la invitación de Google Classroom a {user.email}")
        
        # Si la invitación fue exitosa o el usuario ya está inscrito, enviar el correo
        context = {
            "first_name": user.first_name,
            "last_name": user.last_name,
            "full_name": user.get_full_name(),
            "course_name": course_name,
            "course_id": course_id,
            "user_email": user.email,
            "app_host": settings.APP_HOST,
            "order_id": order.oid,
            "already_enrolled": invitation_result.get("status") == "already_enrolled"
        }

        # Renderizar el template del correo
        html_content = render_to_string("emails/classroom_invitation.html", context)
        
        # Determinar el asunto basado en si ya estaba inscrito o no
        if invitation_result.get("status") == "already_enrolled":
            subject = f"Acceso confirmado a tu curso: {course_name}"
        else:
            subject = f"¡Invitación a tu curso de Google Classroom: {course_name}!"

        # Crear y enviar el correo
        email_message = EmailMessage(
            subject=subject,
            body=html_content,
            from_email=settings.DEFAULT_FROM_EMAIL,
            to=[user.email],
            reply_to=[settings.DEFAULT_FROM_EMAIL],
        )

        email_message.content_subtype = "html"
        email_message.send(fail_silently=False)
        
        return {
            "success": True,
            "message": f"Correo de invitación enviado exitosamente a {user.email}",
            "invitation_status": invitation_result.get("status", "sent"),
            "course_name": course_name
        }
        
    except Order.DoesNotExist:
        error_msg = f"Orden con ID {order_id} no encontrada"
        print(error_msg)
        raise Exception(error_msg)
    except Exception as e:
        error_msg = f"Error al enviar invitación de Google Classroom: {str(e)}"
        print(error_msg)
        raise Exception(error_msg)


@shared_task
def process_classroom_invitation_for_order(order_id):
    """
    Procesa la invitación de Google Classroom para una orden específica.
    Verifica que la orden tenga productos con referencia externa (ext_reference) 
    y envía las invitaciones correspondientes.
    
    Args:
        order_id (str): ID de la orden
    """
    try:
        order = Order.objects.get(oid=order_id)
        user = order.owner
        
        # Validar que el usuario tenga datos completos
        if not user.first_name or not user.last_name:
            raise Exception(f"El usuario debe tener nombre y apellido completos")
        
        if not user.email or not user.email.lower().endswith('@gmail.com'):
            raise Exception(f"El usuario debe tener un correo de Gmail válido")
        
        # Obtener todos los items de la orden
        order_items = order.items.filter(deleted=False)
        
        invitations_sent = []
        
        for item in order_items:
            offering = item.offering
            
            # Verificar si el offering tiene referencia externa (course_id)
            if offering.ext_reference:
                try:
                    # Enviar invitación para este curso
                    result = send_classroom_invitation_email.delay(
                        order_id=str(order.oid),
                        course_id=offering.ext_reference,
                        course_name=offering.name
                    )
                    
                    invitations_sent.append({
                        "offering_name": offering.name,
                        "course_id": offering.ext_reference,
                        "task_id": result.id
                    })
                    
                except Exception as e:
                    print(f"Error al procesar invitación para {offering.name}: {str(e)}")
                    continue
        
        if not invitations_sent:
            print(f"No se encontraron cursos con referencia externa para la orden {order_id}")
            return {
                "success": False,
                "message": "No se encontraron cursos con referencia externa",
                "invitations_sent": []
            }
        
        return {
            "success": True,
            "message": f"Se procesaron {len(invitations_sent)} invitaciones",
            "invitations_sent": invitations_sent
        }
        
    except Order.DoesNotExist:
        error_msg = f"Orden con ID {order_id} no encontrada"
        print(error_msg)
        raise Exception(error_msg)
    except Exception as e:
        error_msg = f"Error al procesar invitaciones de Google Classroom: {str(e)}"
        print(error_msg)
        raise Exception(error_msg)
