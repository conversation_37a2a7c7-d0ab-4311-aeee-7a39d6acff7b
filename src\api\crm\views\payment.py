from core.models import Payment, File
from rest_framework import viewsets, status, filters
from rest_framework.response import Response
from rest_framework.authentication import TokenAuthentication
from rest_framework.permissions import IsAuthenticated
from rest_framework.decorators import action
from api.crm.serializers.payment import (
    CrmPaymentBaseSerializer,
    CrmPaymentListItemSerializer,
    CrmPaymentRetrieveSerializer,
    CrmPaymentCreateSerializer,
    CrmPaymentUpdateSerializer,
)
from api.shared.serializers.file import FileSerializer
from api.mixins import AuditMixin, SwaggerTagMixin
from api.crm.services.file import upload_file_to_minio
from api.crm.filters.payment import CrmPaymentFilter
from drf_yasg.utils import swagger_auto_schema
from drf_yasg import openapi
from django_filters.rest_framework import DjangoFilterBackend
from api.paginations import StandardResultsPagination
from api.permissions import IsStaffUser


class CrmPaymentViewSet(
    AuditMixin,
    SwaggerTagMixin,
    viewsets.ModelViewSet,
):
    model_class = Payment
    queryset = Payment.objects.filter(deleted=False).order_by("-created_at")
    serializer_class = CrmPaymentBaseSerializer

    authentication_classes = [TokenAuthentication]
    permission_classes = [IsAuthenticated & IsStaffUser]
    pagination_class = StandardResultsPagination

    filterset_class = CrmPaymentFilter
    filter_backends = (
        DjangoFilterBackend,
        filters.SearchFilter,
        filters.OrderingFilter,
    )
    ordering_fields = ["created_at"]
    search_fields = [
        "order__owner__first_name",
        "order__owner__last_name",
        "order__owner__email",
        "order__owner__phone_number",
    ]

    swagger_tags = ["Payments"]

    def get_serializer(self, *args, **kwargs):
        if self.action == "create":
            return CrmPaymentCreateSerializer(*args, **kwargs)
        elif self.action == "update" or self.action == "partial_update":
            return CrmPaymentUpdateSerializer(*args, **kwargs)
        elif self.action == "retrieve":
            return CrmPaymentRetrieveSerializer(*args, **kwargs)
        elif self.action == "list":
            return CrmPaymentListItemSerializer(*args, **kwargs)
        return CrmPaymentBaseSerializer(*args, **kwargs)

    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        self.perform_create(serializer)
        retrieve_serializer = CrmPaymentRetrieveSerializer(serializer.instance)
        return Response(retrieve_serializer.data, status=status.HTTP_201_CREATED)

    @swagger_auto_schema(
        operation_description="Upload a single voucher file",
        request_body=openapi.Schema(
            type=openapi.TYPE_OBJECT,
            required=["file"],
            properties={
                "file": openapi.Schema(
                    type=openapi.TYPE_FILE, description="The voucher file to upload"
                )
            },
        ),
        responses={
            201: FileSerializer,
            400: openapi.Response(
                description="Bad Request - No file provided",
                examples={
                    "application/json": {
                        "error": "No se ha proporcionado ningún archivo"
                    }
                },
            ),
        },
    )
    @action(detail=False, methods=["POST"], url_path="upload-voucher")
    def upload_voucher(self, request, *args, **kwargs):

        if "file" not in request.FILES:
            return Response(
                {"error": "No se ha proporcionado ningún archivo"},
                status=status.HTTP_400_BAD_REQUEST,
            )

        file_obj = request.FILES["file"]
        fid, objet_name = upload_file_to_minio(file_obj)
        file = File.objects.create(
            fid=fid,
            is_used=False,
            is_private=True,
            name=objet_name.split("/")[-1],
            bucket_name="private",
            object_name=objet_name,
        )
        file.save()

        data = FileSerializer(file).data
        return Response(data, status=status.HTTP_201_CREATED)

    @action(detail=False, methods=["DELETE"], url_path="remove-voucher/(?P<fid>[^/.]+)")
    def remove_voucher(self, request, *args, **kwargs):
        fid = kwargs.get("fid")
        file = File.objects.get(fid=fid)
        file.delete()
        return Response(status=status.HTTP_204_NO_CONTENT)
