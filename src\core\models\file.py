import uuid
from django.db import models
from django.conf import settings
from core.models.base import AuditBaseModel
from core.utils import get_object_size, get_object_content_type, get_presigned_url


class File(AuditBaseModel):
    fid = models.UUIDField(
        primary_key=True,
        editable=False,
        default=uuid.uuid4,
        verbose_name="File ID",
        help_text="Unique identifier for the file",
    )
    name = models.CharField(
        max_length=255,
        blank=True,
        help_text="Name of the file",
    )
    description = models.TextField(
        blank=True,
        help_text="Description of the file",
    )

    bucket_name = models.CharField(
        max_length=255,
        blank=False,
        help_text="Bucket name of the file",
    )

    object_name = models.CharField(
        max_length=255,
        blank=False,
        help_text="Object name of the file",
    )

    is_private = models.BooleanField(
        default=False,
        help_text="Is the file private",
    )

    width = models.IntegerField(
        blank=True,
        null=True,
        help_text="Width of the image file",
    )
    height = models.IntegerField(
        blank=True,
        null=True,
        help_text="Height of the image",
    )

    is_used = models.BooleanField(
        default=True,
        help_text="Indicates if the file is being used",
    )

    @property
    def url(self):
        if self.is_private:
            return get_presigned_url(self.bucket_name, self.object_name)
        if self.bucket_name and self.object_name:
            return f"{settings.MINIO_BASE_URL}/{self.bucket_name}/{self.object_name}"
        return None

    @property
    def format(self):
        if not self.name or len(self.name.split(".")) == 1:
            return None
        return self.name.split(".")[-1].upper()

    @property
    def size(self):
        """Return file size in bytes, or None if file doesn't exist or there's an error."""
        return get_object_size(self.bucket_name, self.object_name)

    @property
    def content_type(self):
        """Return file content type, or None if file doesn't exist or there's an error."""
        return get_object_content_type(self.bucket_name, self.object_name)

    @property
    def exists(self):
        """Check if the file exists in the storage by trying to get its size."""
        return self.size is not None

    class Meta:
        verbose_name = "File"
        verbose_name_plural = "Files"

    def __str__(self):
        if self.name:
            return self.name
        if self.bucket_name and self.object_name:
            return f"{self.bucket_name}/{self.object_name}"
        return f"File({self.fid})"
