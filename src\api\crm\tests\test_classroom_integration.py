from django.test import TestCase
from unittest.mock import patch, MagicMock
from django.contrib.auth import get_user_model
from core.models import Order, OrderItem, Offering
from services.google.classroom import GoogleClassroomManager
from api.crm.tasks.classroom import send_classroom_invitation_email, process_classroom_invitation_for_order
from api.crm.serializers.order import CrmUpdateOrderSerializer
from rest_framework.exceptions import ValidationError

User = get_user_model()


class GoogleClassroomManagerTest(TestCase):
    """Tests para GoogleClassroomManager"""

    def setUp(self):
        self.manager = GoogleClassroomManager()

    def test_validate_gmail_email_valid(self):
        """Test validación de email de Gmail válido"""
        valid_emails = [
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>"
        ]
        
        for email in valid_emails:
            with self.subTest(email=email):
                self.assertTrue(self.manager.validate_gmail_email(email))

    def test_validate_gmail_email_invalid(self):
        """Test validación de email de Gmail inválido"""
        invalid_emails = [
            "<EMAIL>",
            "<EMAIL>",
            "<EMAIL>",
            "test@gmailcom",
            "",
            None,
            "invalid-email"
        ]
        
        for email in invalid_emails:
            with self.subTest(email=email):
                self.assertFalse(self.manager.validate_gmail_email(email))


class OrderSerializerClassroomValidationTest(TestCase):
    """Tests para validaciones de Google Classroom en OrderSerializer"""

    def setUp(self):
        # Crear usuario con datos completos
        self.user_complete = User.objects.create_user(
            username="testuser",
            email="<EMAIL>",
            first_name="John",
            last_name="Doe"
        )
        
        # Crear usuario sin nombre
        self.user_no_name = User.objects.create_user(
            username="testuser2",
            email="<EMAIL>",
            first_name="",
            last_name="Doe"
        )
        
        # Crear usuario sin email de Gmail
        self.user_no_gmail = User.objects.create_user(
            username="testuser3",
            email="<EMAIL>",
            first_name="Jane",
            last_name="Smith"
        )
        
        # Crear offering
        self.offering = Offering.objects.create(
            name="Test Course",
            start_date="2024-01-01",
            end_date="2024-12-31",
            base_price=100.00,
            foreign_base_price=25.00,
            discount=0.00,
            ext_reference="test_course_123"
        )
        
        # Crear orden con usuario completo
        self.order_complete = Order.objects.create(
            owner=self.user_complete,
            stage=Order.PROSPECT_STAGE
        )
        OrderItem.objects.create(order=self.order_complete, offering=self.offering)

    def test_validate_sold_stage_with_complete_user(self):
        """Test validación exitosa con usuario completo"""
        serializer = CrmUpdateOrderSerializer(instance=self.order_complete)
        
        # No debería lanzar excepción
        try:
            validated_stage = serializer.validate_stage(Order.SOLD_STAGE)
            self.assertEqual(validated_stage, Order.SOLD_STAGE)
        except ValidationError:
            self.fail("validate_stage() raised ValidationError unexpectedly!")

    def test_validate_sold_stage_with_incomplete_user(self):
        """Test validación fallida con usuario sin nombre"""
        order_incomplete = Order.objects.create(
            owner=self.user_no_name,
            stage=Order.PROSPECT_STAGE
        )
        
        serializer = CrmUpdateOrderSerializer(instance=order_incomplete)
        
        with self.assertRaises(ValidationError) as context:
            serializer.validate_stage(Order.SOLD_STAGE)
        
        errors = context.exception.detail
        self.assertIn('owner_first_name', errors)

    def test_validate_sold_stage_with_non_gmail_user(self):
        """Test validación fallida con usuario sin Gmail"""
        order_no_gmail = Order.objects.create(
            owner=self.user_no_gmail,
            stage=Order.PROSPECT_STAGE
        )
        
        serializer = CrmUpdateOrderSerializer(instance=order_no_gmail)
        
        with self.assertRaises(ValidationError) as context:
            serializer.validate_stage(Order.SOLD_STAGE)
        
        errors = context.exception.detail
        self.assertIn('owner_email', errors)
        self.assertIn('Gmail', str(errors['owner_email']))


class ClassroomTasksTest(TestCase):
    """Tests para las tareas de Celery de Google Classroom"""

    def setUp(self):
        self.user = User.objects.create_user(
            username="testuser",
            email="<EMAIL>",
            first_name="John",
            last_name="Doe"
        )
        
        self.offering = Offering.objects.create(
            name="Test Course",
            start_date="2024-01-01",
            end_date="2024-12-31",
            base_price=100.00,
            foreign_base_price=25.00,
            discount=0.00,
            ext_reference="test_course_123"
        )
        
        self.order = Order.objects.create(
            owner=self.user,
            stage=Order.SOLD_STAGE
        )
        OrderItem.objects.create(order=self.order, offering=self.offering)

    @patch('services.google.classroom.GoogleClassroomManager.invite_student_to_course')
    @patch('services.google.classroom.GoogleClassroomManager.get_course_info')
    @patch('django.core.mail.EmailMessage.send')
    def test_send_classroom_invitation_email_success(self, mock_email_send, mock_get_course, mock_invite):
        """Test envío exitoso de invitación por email"""
        # Mock responses
        mock_get_course.return_value = {"name": "Test Course", "id": "test_course_123"}
        mock_invite.return_value = {"status": "sent"}
        mock_email_send.return_value = True
        
        # Ejecutar tarea
        result = send_classroom_invitation_email(
            order_id=str(self.order.oid),
            course_id="test_course_123",
            course_name="Test Course"
        )
        
        # Verificar resultado
        self.assertTrue(result['success'])
        self.assertIn('exitosamente', result['message'])
        
        # Verificar que se llamaron los métodos
        mock_invite.assert_called_once_with("test_course_123", "<EMAIL>")
        mock_email_send.assert_called_once()

    @patch('api.crm.tasks.classroom.send_classroom_invitation_email.delay')
    def test_process_classroom_invitation_for_order(self, mock_send_task):
        """Test procesamiento de invitaciones para una orden"""
        # Mock task result
        mock_task_result = MagicMock()
        mock_task_result.id = "task_123"
        mock_send_task.return_value = mock_task_result
        
        # Ejecutar tarea
        result = process_classroom_invitation_for_order(str(self.order.oid))
        
        # Verificar resultado
        self.assertTrue(result['success'])
        self.assertEqual(len(result['invitations_sent']), 1)
        self.assertEqual(result['invitations_sent'][0]['offering_name'], "Test Course")
        self.assertEqual(result['invitations_sent'][0]['course_id'], "test_course_123")
        
        # Verificar que se llamó la tarea
        mock_send_task.assert_called_once()
