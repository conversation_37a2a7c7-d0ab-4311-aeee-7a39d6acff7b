# Integración con Google Classroom

Esta documentación describe la implementación de la integración con Google Classroom para enviar invitaciones automáticas cuando una orden cambia al estado "sold" (vendido).

## Componentes Implementados

### 1. GoogleClassroomManager (`src/services/google/classroom.py`)

Clase principal para gestionar las operaciones con Google Classroom API.

**Características principales:**
- Validación de correos Gmail
- Envío de invitaciones a cursos
- Verificación de estado de inscripción
- Gestión de invitaciones pendientes

**Métodos principales:**
- `validate_gmail_email(email)`: Valida que el correo sea de Gmail
- `invite_student_to_course(course_id, student_email)`: Envía invitación a un curso
- `get_course_info(course_id)`: Obtiene información de un curso
- `check_student_enrollment_status(course_id, student_email)`: Verifica estado de inscripción

### 2. Ta<PERSON><PERSON> de Celery (`src/api/crm/tasks/classroom.py`)

Tareas asíncronas para el procesamiento de invitaciones.

**Tareas disponibles:**
- `send_classroom_invitation_email`: Envía correo de invitación individual
- `process_classroom_invitation_for_order`: Procesa todas las invitaciones de una orden

### 3. Validaciones en OrderSerializer (`src/api/crm/serializers/order.py`)

Validaciones automáticas cuando una orden cambia a estado "sold".

**Validaciones implementadas:**
- Usuario debe tener nombre y apellido
- Usuario debe tener correo de Gmail válido (@gmail.com)
- Envío automático de invitaciones para offerings con `ext_reference`

### 4. Template de Correo (`src/templates/emails/classroom_invitation.html`)

Template HTML para el correo de invitación con:
- Información del curso
- Instrucciones de acceso
- Enlaces a Google Classroom
- Diseño responsive

## Configuración Requerida

### 1. Credenciales de Google

Asegurar que el archivo `src/secrets/credentials.json` tenga los permisos necesarios:

```json
{
  "type": "service_account",
  "project_id": "your-project-id",
  "private_key_id": "...",
  "private_key": "...",
  "client_email": "<EMAIL>",
  "client_id": "...",
  "auth_uri": "https://accounts.google.com/o/oauth2/auth",
  "token_uri": "https://oauth2.googleapis.com/token"
}
```

### 2. Scopes Requeridos

La Service Account debe tener los siguientes scopes:
- `https://www.googleapis.com/auth/classroom.courses`
- `https://www.googleapis.com/auth/classroom.rosters`
- `https://www.googleapis.com/auth/classroom.profile.emails`

### 3. Configuración del Offering

Para que un offering envíe invitaciones automáticas, debe tener configurado el campo `ext_reference` con el ID del curso de Google Classroom.

```python
offering = Offering.objects.create(
    name="Curso de Python",
    ext_reference="abc123def456",  # ID del curso en Google Classroom
    # ... otros campos
)
```

## Flujo de Funcionamiento

### 1. Actualización de Orden a "Sold"

Cuando se actualiza una orden al estado "sold":

1. **Validación previa:**
   - Se verifica que el owner tenga nombre y apellido
   - Se verifica que el owner tenga email de Gmail válido

2. **Si las validaciones pasan:**
   - Se actualiza la orden normalmente
   - Se envía una tarea asíncrona para procesar invitaciones

3. **Si las validaciones fallan:**
   - Se retorna error con detalles específicos
   - La orden no se actualiza

### 2. Procesamiento de Invitaciones

La tarea `process_classroom_invitation_for_order`:

1. Obtiene todos los items de la orden
2. Para cada offering con `ext_reference`:
   - Envía invitación a Google Classroom
   - Envía correo de notificación al usuario
3. Maneja errores sin bloquear otras invitaciones

### 3. Envío de Correo

El correo incluye:
- Información del curso
- Instrucciones para aceptar la invitación
- Enlaces directos a Google Classroom
- Información de contacto de soporte

## Uso de la API

### Actualizar Orden a Sold

```python
# Ejemplo de actualización exitosa
data = {
    "stage": "sold"
}

# Si el usuario tiene datos completos y Gmail, la actualización será exitosa
# y se enviarán las invitaciones automáticamente
```

### Respuesta de Error

```json
{
    "owner_first_name": ["El propietario de la orden debe tener nombre."],
    "owner_last_name": ["El propietario de la orden debe tener apellido."],
    "owner_email": ["El propietario de la orden debe tener un correo de Gmail válido (@gmail.com)."]
}
```

## Testing

Ejecutar los tests de integración:

```bash
python manage.py test api.crm.tests.test_classroom_integration
```

Los tests cubren:
- Validación de emails de Gmail
- Validaciones del serializer
- Funcionamiento de las tareas de Celery
- Envío de correos

## Monitoreo y Logs

### Logs de Celery

Las tareas registran información en los logs de Celery:

```
INFO: Invitación enviada <NAME_EMAIL> para el curso abc123
INFO: Tarea de invitación de Google Classroom enviada para la orden uuid-123
```

### Manejo de Errores

Los errores se registran pero no bloquean la actualización de la orden:

```
ERROR: Error al enviar invitación: Usuario ya inscrito en el curso
ERROR: Error al procesar invitación para Curso Python: Curso no encontrado
```

## Consideraciones Importantes

### 1. Limitaciones de Gmail

- Solo se aceptan correos @gmail.com
- Los usuarios deben tener cuenta de Google activa
- Las invitaciones pueden ir a spam

### 2. Gestión de Errores

- Los errores en invitaciones no bloquean la venta
- Se registran todos los errores para seguimiento
- Los usuarios pueden ser invitados manualmente si falla el proceso automático

### 3. Performance

- Las invitaciones se procesan de forma asíncrona
- No afecta el tiempo de respuesta de la API
- Se pueden procesar múltiples invitaciones en paralelo

### 4. Seguridad

- Las credenciales se almacenan de forma segura
- Se valida la existencia de cursos antes de enviar invitaciones
- Se previene el spam verificando el estado de inscripción

## Troubleshooting

### Problema: Usuario no recibe invitación

**Posibles causas:**
1. Correo no es de Gmail
2. Correo en carpeta de spam
3. Curso no existe en Google Classroom
4. Usuario ya inscrito en el curso

**Solución:**
1. Verificar que el correo sea @gmail.com
2. Pedir al usuario revisar spam
3. Verificar que `ext_reference` sea correcto
4. Usar `check_student_enrollment_status` para verificar estado

### Problema: Error de permisos

**Posibles causas:**
1. Service Account sin permisos
2. Scopes incorrectos
3. Credenciales expiradas

**Solución:**
1. Verificar permisos en Google Cloud Console
2. Actualizar scopes en la configuración
3. Regenerar credenciales si es necesario
