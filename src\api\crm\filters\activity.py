from django_filters import rest_framework as filters
from core.models import Activity


class CrmActivityFilter(filters.FilterSet):
    """Filter set for Activity model with custom filtering methods"""

    status = filters.CharFilter(method="filter_by_status")
    order = filters.CharFilter(method="filter_by_order")
    responsible = filters.CharFilter(method="filter_by_responsible")
    overdue = filters.BooleanFilter(method="filter_by_overdue")
    has_deadline = filters.BooleanFilter(method="filter_has_deadline")

    def filter_by_order(self, queryset, name, value):
        """Filter activities by order ID"""
        return queryset.filter(order__oid=value)

    def filter_by_status(self, queryset, name, value):
        """Filter activities by status"""
        valid_statuses = [choice[0] for choice in Activity.STATUS_CHOICES]
        if value in valid_statuses:
            return queryset.filter(status=value)
        return queryset

    def filter_by_responsible(self, queryset, name, value):
        """Filter activities by responsible user ID"""
        return queryset.filter(responsible__uid=value)

    def filter_by_overdue(self, queryset, name, value):
        """Filter activities that are overdue"""
        from django.utils import timezone

        if value is True:
            return queryset.filter(
                deadline__lt=timezone.now(),
                status__in=[Activity.PENDING_STATUS, Activity.IN_PROGRESS_STATUS],
            )
        elif value is False:
            return queryset.exclude(
                deadline__lt=timezone.now(),
                status__in=[Activity.PENDING_STATUS, Activity.IN_PROGRESS_STATUS],
            )
        return queryset

    def filter_has_deadline(self, queryset, name, value):
        """Filter activities based on whether they have a deadline"""
        if value is True:
            return queryset.filter(deadline__isnull=False)
        elif value is False:
            return queryset.filter(deadline__isnull=True)
        return queryset

    class Meta:
        model = Activity
        fields = [
            "status",
            "order",
            "responsible",
            "overdue",
            "has_deadline",
        ]
