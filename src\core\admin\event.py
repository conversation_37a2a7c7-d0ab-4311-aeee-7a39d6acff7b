from django.contrib import admin
from core.models import Event, EventSchedule, EventScheduleEnrollment


@admin.register(Event)
class EventAdmin(admin.ModelAdmin):
    list_display = [
        "name",
        "description",
        "created_at",
        "updated_at",
    ]
    search_fields = ["name", "description"]
    ordering = ["-created_at"]

    readonly_fields = [
        "created_at",
        "updated_at",
        "deleted_at",
        "deleted_by",
    ]


@admin.register(EventSchedule)
class EventScheduleAdmin(admin.ModelAdmin):
    list_display = [
        "name",
        "description",
        "start_date",
        "end_date",
        "stage",
        "created_at",
        "updated_at",
    ]
    search_fields = ["name", "description"]
    list_filter = ["is_general", "stage"]
    ordering = ["-created_at"]
    readonly_fields = [
        "created_at",
        "updated_at",
        "deleted_at",
        "deleted_by",
    ]
    # inlines = [PartnershipInline]
    exclude = (
        "partnerships",
    )  # Evita mostrar el campo partnerships directamente en el formulario


@admin.register(EventScheduleEnrollment)
class EventScheduleEnrollmentAdmin(admin.ModelAdmin):
    list_display = [
        "id",
        "user",
        "first_name",
        "last_name",
        "email",
        "phone_number",
        "created_at",
        "updated_at",
    ]
    search_fields = ["user__email"]
    ordering = ["-created_at"]
    readonly_fields = [
        "created_at",
        "updated_at",
        "deleted_at",
        "deleted_by",
    ]
